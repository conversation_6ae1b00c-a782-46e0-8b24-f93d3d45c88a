package com.ms.bp.application;

import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.domain.permission.PermissionService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.permission.model.JobCombination;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.master.model.UserBasicInfo;
import com.ms.bp.domain.master.model.ConcurrentJobInfo;
import com.ms.bp.domain.master.repository.UserMasterRepository;
import com.ms.bp.domain.master.repository.SystemAdminRepository;
import com.ms.bp.domain.master.repository.ConcurrentJobRepository;
import com.ms.bp.domain.master.repository.UnitMasterRepository;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.domain.master.model.UnitMasterInfo;
import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.model.UnitGroupInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponse;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.PermissionCodeParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 権限アプリケーションサービス
 * 権限関連のユースケースを調整し、ドメインサービスを組み合わせて処理を実行する
 * 権限は読み取り専用操作のため、読み取り専用実行を使用する
 */
public class PermissionApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionApplicationService.class);
    /** MS戦略・コーポOF判定コード */
    private static final String MS_STRATEGY_CORPO_HANT_CODE = "2";

    // 役職区分名称マッピング（簡易実装）
    private static final Map<String, String> POSITION_NAME_MAPPING = Map.of(
            "01", "一般",
            "02", "主任",
            "03", "係長",
            "04", "課長",
            "05", "部長",
            "06", "取締役",
            "07", "常務",
            "08", "専務",
            "09", "社長"
    );


    /**
     * ユーザーの全権限を取得（新業務要件対応版）
     * ロールリスト構造のレスポンスを返却する
     *
     * @param userInfo 認証済みユーザー情報
     * @param unitCode ユニットコード（空の場合は動的取得）
     * @param positionCode 役職区分コード（空の場合は動的取得）
     * @param areaCode エリアコード（空の場合は動的取得）
     * @param groupCode グループコード（空の場合は動的取得）
     * @param systemAdminFlag システム管理者フラグ（"1":管理者、その他:一般）
     * @param operationType 操作種別
     * @return ユーザー権限レスポンス
     */
    public UserPermissionsResponseV2 getUserPermissions(UserInfo userInfo,
                                                       String unitCode,
                                                       String positionCode,
                                                       String areaCode,
                                                       String groupCode,
                                                       String systemAdminFlag,
                                                       String operationType) {
        logger.info("新業務要件権限取得開始: ユーザーID={}, systemAdminFlag={}",
                   userInfo.getShainCode(), systemAdminFlag);

        // ユーザーのユニットコード、役職区分コード、エリアコード、グループコードを取得
        JobCombination primaryJob = getPrimaryJobCombination(userInfo, unitCode, positionCode, areaCode, groupCode);

        // システム管理者判定
        boolean isSystemAdmin = determineSystemAdmin(userInfo, systemAdminFlag, unitCode, positionCode, areaCode, groupCode);

        if (isSystemAdmin) {
            // システム管理者の場合
            return createSystemAdminResponse(userInfo, primaryJob, operationType);
        } else {
            // 一般ユーザーの場合：兼務情報を含むロールリストを作成
            List<JobCombination> allJobCombinations = getAllJobCombinations(userInfo, primaryJob);
            return createRoleBasedResponse(userInfo, allJobCombinations, operationType);
        }
    }

    /**
     * 主職務の組み合わせを取得
     * パラメータが空の場合は社員マスタから動的取得
     */
    private JobCombination getPrimaryJobCombination(UserInfo userInfo, String unitCode, String positionCode, String areaCode, String groupCode) {
        // パラメータが空の場合、社員マスタから取得
        if (!isParametersProvided(unitCode, positionCode, areaCode, groupCode)) {
            Optional<UserBasicInfo> userBasicInfo = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                UserMasterRepository userMasterRepository = serviceFactory.getUserMasterRepository();
                return userMasterRepository.findUserBasicInfo(userInfo.getSystemOperationCompanyCode(), userInfo.getShainCode());
            });

            if (userBasicInfo.isPresent()) {
                UserBasicInfo basicInfo = userBasicInfo.get();
                return JobCombination.builder()
                        .unitCode(basicInfo.getUnitCode())
                        .positionCode(basicInfo.getPositionCode())
                        .areaCode(basicInfo.getAreaCode())
                        .groupCode(basicInfo.getGroupCode())
                        .build();
            }
        } else {
            // パラメータが提供されている場合はそれを使用
            return JobCombination.builder()
                    .unitCode(unitCode)
                    .positionCode(positionCode)
                    .areaCode(areaCode)
                    .groupCode(groupCode)
                    .build();
        }
        return null;
    }

    /**
     * システム管理者用レスポンス作成（新構造対応）
     */
    private UserPermissionsResponseV2 createSystemAdminResponse(UserInfo userInfo, JobCombination primaryJob, String operationType) {
        logger.info("システム管理者レスポンス作成開始: 社員コード={}", userInfo.getShainCode());

        // 全ての有効な本社権限を取得
        List<Permission> headOfficePermissions = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionRepository permissionRepository = serviceFactory.getPermissionRepository();
            return permissionRepository.findAllValidHeadOfficePermissions(userInfo.getSystemOperationCompanyCode());
        });

        // 権限リスト変換
        List<UserPermissionsResponseV2.PermissionInfo> permissionList = headOfficePermissions.stream()
                .map(this::convertToPermissionInfo)
                .collect(Collectors.toList());

        // 操作種別でフィルタリング
        if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType) || BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
            permissionList = permissionList.stream()
                    .filter(p -> operationType.equals(p.getOperationDivision()))
                    .toList();
        }
        // ロール情報を作成
        UserPermissionsResponseV2.RoleInfo roleInfo = createRoleInfo(primaryJob, permissionList, userInfo.getSystemOperationCompanyCode());

        return UserPermissionsResponseV2.builder()
                .systemOperationCompanyCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .screenDisplayFlag("0") // 表示可
                .systemAdminFlag("1") // システム管理者
                .roleList(List.of(roleInfo))
                .build();
    }

    /**
     * ロール情報を作成
     */
    private UserPermissionsResponseV2.RoleInfo createRoleInfo(JobCombination job, List<UserPermissionsResponseV2.PermissionInfo> permissions,
                                                              String systemOperationCompanyCode) {

        // ユニット・グループ情報を取得
        UnitGroupInfo unitGroupInfo = getUnitGroupInfo(job.getUnitCode());

        // 読み取り専用でデータベースリソースを使用してMS戦略・コーポOF判定コードに対応する種類コードリストを取得
        List<String> ruleType = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionRepository  permissionRepository = serviceFactory.getPermissionRepository();
            return permissionRepository.findRuleTypeCodesByMsStrategyCorp(MS_STRATEGY_CORPO_HANT_CODE, systemOperationCompanyCode);
        });
        //ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外
        boolean isRuleType = !ruleType.isEmpty() && !ruleType.contains(job.getUnitCode());

        //役職区分判定要否 0:否 1:要
        String positionSpecialCheck  = needsSpecialCheck(permissions, isRuleType);

        // エリアリストを作成
        List<UserPermissionsResponseV2.AreaInfo> areaList = createAreaList(permissions, systemOperationCompanyCode, job.getAreaCode());

        return UserPermissionsResponseV2.RoleInfo.builder()
                .areaCode(job.getAreaCode())
                .unitCode(job.getUnitCode())
                .unitName(unitGroupInfo.getUnitName())
                .groupCode(unitGroupInfo.getGroupCode())
                .groupName(unitGroupInfo.getGroupName())
                .positionSpecialCheck(positionSpecialCheck)
                .positionCode(job.getPositionCode())
                .positionName(getPositionName(job.getPositionCode()))
                .permissionList(permissions)
                .areaList(areaList)
                .build();
    }

    /**
     * エリアリストを作成
     */
    private List<UserPermissionsResponseV2.AreaInfo> createAreaList(List<UserPermissionsResponseV2.PermissionInfo> permissions,
                                                                    String systemOperationCompanyCode,
                                                                    String primaryAreaCode) {
        // エリア担当者権限があるかチェック
        boolean hasAreaSpecificPermission = permissions.stream()
                .anyMatch(permission -> BusinessConstants.AREA_PATTERN_AREA_SPECIFIC.equals(permission.getAreaPattern()));

        if (hasAreaSpecificPermission) {
            // エリア担当者権限がある場合、権限ルールマスタから取得
            List<AreaInfo> areaInfos = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                PermissionRepository permissionRepository = serviceFactory.getPermissionRepository();
                return permissionRepository.findAreaInfosByAreaTantoshaPermission(systemOperationCompanyCode);
            });

            return areaInfos.stream()
                    .map(areaInfo -> UserPermissionsResponseV2.AreaInfo.builder()
                            .areaCode(areaInfo.getAreaCode())
                            .areaName(areaInfo.getAreaName())
                            .build())
                    .collect(Collectors.toList());
        } else {
            // エリア名を取得するためにエリアマスタから取得
            String areaName = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                AreaCodeRepository areaCodeRepository = serviceFactory.getAreaCodeRepository();
                return areaCodeRepository.findAreaNameByAreaCode(primaryAreaCode).orElse("");
            });

            return List.of(UserPermissionsResponseV2.AreaInfo.builder()
                    .areaCode(primaryAreaCode)
                    .areaName(areaName)
                    .build());
        }
    }

    /**
     * 指定されたルールに基づき、特別なチェック（役職区分判定要否）が必要かどうかを判定します。
     *
     * @param permissions ユーザーの権限リスト
     * @return "1" (要), "0" (否)
     */
    public static String needsSpecialCheck(List<UserPermissionsResponseV2.PermissionInfo> permissions, boolean isRuleType) {
        // 条件：ファイルタイプが "002"/"003" かつ エリア権限（3桁目 'A'） かつ ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外場合
        return permissions.stream()
                .anyMatch(p -> {
                    var code = p.getPermissionCode();
                    return (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(p.getFileType()) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(p.getFileType())) &&
                            code != null && code.length() > 2 && code.charAt(2) == 'A' && isRuleType;
                }) ? "1" : "0";
    }

    /**
     * パラメータが提供されているかチェック
     */
    private boolean isParametersProvided(String unitCode, String positionCode, String areaCode, String groupCode) {
        return unitCode != null && !unitCode.trim().isEmpty() &&
               positionCode != null && !positionCode.trim().isEmpty() &&
               areaCode != null && !areaCode.trim().isEmpty() &&
               groupCode != null && !groupCode.trim().isEmpty();
    }

    /**
     * 全ての職務組み合わせ（主職務+兼務）を取得
     */
    private List<JobCombination> getAllJobCombinations(UserInfo userInfo, JobCombination primaryJob) {
        List<JobCombination> allJobs = new ArrayList<>();

        // 主職務を追加
        allJobs.add(primaryJob);

        // 兼務情報を取得
        List<ConcurrentJobInfo> concurrentJobs = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            ConcurrentJobRepository concurrentJobRepository = serviceFactory.getConcurrentJobRepository();
            return concurrentJobRepository.findConcurrentJobsWithAreaInfo(userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode());
        });

        // 兼務情報を JobCombination に変換
        for (ConcurrentJobInfo concurrentJob : concurrentJobs) {
            JobCombination job = JobCombination.builder()
                .unitCode(concurrentJob.getUnitCode())
                .positionCode(concurrentJob.getPositionDivision())
                .areaCode(concurrentJob.getAreaCode())
                .groupCode(concurrentJob.getGroupCode())
                .build();
            allJobs.add(job);
        }

        logger.debug("職務組み合わせ取得完了: 主職務=1, 兼務={}, 合計={}", concurrentJobs.size(), allJobs.size());
        return allJobs;
    }

    /**
     * 指定職務組み合わせの権限を取得
     */
    private List<UserPermissionInfo> getPermissionsForJobCombination(UserInfo userInfo, JobCombination job) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionService permissionService = serviceFactory.createPermissionService();

            // 一時的なUserInfoを作成（職務組み合わせの情報を設定）
            UserInfo tempUserInfo = new UserInfo();
            tempUserInfo.setShainCode(userInfo.getShainCode());
            tempUserInfo.setSystemOperationCompanyCode(userInfo.getSystemOperationCompanyCode());
            tempUserInfo.setUnitCode(job.getUnitCode());
            tempUserInfo.setPositionCode(job.getPositionCode());
            tempUserInfo.setAreaCode(job.getAreaCode());
            tempUserInfo.setGroupCode(job.getGroupCode());

            return permissionService.getUserPermissions(tempUserInfo);
        });
    }


    /**
     * ユニットコードからユニット・グループ統合情報を取得（パフォーマンス最適化版）
     * 単一クエリでユニット名とグループ名を同時取得
     */
    private UnitGroupInfo getUnitGroupInfo(String unitCode) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            UnitMasterRepository unitMasterRepository = serviceFactory.getUnitMasterRepository();
            Optional<UnitGroupInfo> unitGroupInfo = unitMasterRepository.findUnitGroupInfoByUnitCode(unitCode);
            return unitGroupInfo.orElse(UnitGroupInfo.builder()
                .unitCode(unitCode)
                .unitName("")
                .groupCode("")
                .groupName("")
                .build());
        });
    }

    /**
     * 役職区分コードから役職区分名を取得
     */
    private String getPositionName(String positionCode) {
        if (positionCode == null || positionCode.trim().isEmpty()) {
            return "";
        }
        return POSITION_NAME_MAPPING.getOrDefault(positionCode, "");
    }

    /**
     * システム管理者判定ロジック
     */
    private boolean determineSystemAdmin(UserInfo userInfo, String systemAdminFlag,
                                        String unitCode, String positionCode, String areaCode, String groupCode) {
        // パラメータが非空かつシステム管理者フラグが"1"の場合
        if (isParametersProvided(unitCode, positionCode, areaCode, groupCode) && "1".equals(systemAdminFlag)) {
            return true;
        }

        // パラメータシステム管理者フラグが"1"以外の場合、データベースで判定
        if (!"1".equals(systemAdminFlag)) {
            return LambdaResourceManager.executeReadOnly(serviceFactory -> {
                SystemAdminRepository systemAdminRepository = serviceFactory.getSystemAdminRepository();
                return systemAdminRepository.isValidSystemAdmin(userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode());
            });
        }

        return false;
    }

    /**
     * 一般ユーザー用ロールベースレスポンス作成
     */
    private UserPermissionsResponseV2 createRoleBasedResponse(UserInfo userInfo, List<JobCombination> allJobCombinations, String operationType) {
        logger.debug("ロールベースレスポンス作成開始: 職務組み合わせ数={}", allJobCombinations.size());

        List<UserPermissionsResponseV2.RoleInfo> roleList = new ArrayList<>();
        boolean hasAnyPermissions = false;

        // 各職務組み合わせに対してロール情報を作成
        for (JobCombination job : allJobCombinations) {
            List<UserPermissionInfo> jobPermissions = getPermissionsForJobCombination(userInfo, job);

            // 操作種別でフィルタリング
            if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType) || BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
                jobPermissions = jobPermissions.stream()
                    .filter(p -> operationType.equals(p.getOperationDivision()))
                    .toList();
            }

            if (!jobPermissions.isEmpty()) {
                hasAnyPermissions = true;

                // 権限リスト変換
                List<UserPermissionsResponseV2.PermissionInfo> permissionList =
                        jobPermissions.stream().map(p -> UserPermissionsResponseV2.PermissionInfo.builder()
                                .permissionCode(p.getPermissionCode())
                                .operationDivision(p.getOperationDivision())
                                .areaPattern(p.getAreaPattern())
                                .fileType(p.getFileType())
                                .build())
                        .collect(Collectors.toList());

                // ロール情報を作成
                UserPermissionsResponseV2.RoleInfo roleInfo = createRoleInfo(job, permissionList, userInfo.getSystemOperationCompanyCode());
                roleList.add(roleInfo);
            }
        }

        return UserPermissionsResponseV2.builder()
            .systemOperationCompanyCode(userInfo.getSystemOperationCompanyCode())
            .shainCode(userInfo.getShainCode())
            .screenDisplayFlag(hasAnyPermissions ? "1" : "0") // 権限がある場合は表示可
            .systemAdminFlag("0") // 一般ユーザー
            .roleList(roleList)
            .build();
    }



    /**
     * Permissionを新構造のPermissionInfoに変換
     * 共通の権限コード解析ロジックを使用してコード重複を排除
     *
     * @param permission 権限オブジェクト
     * @return 新構造の権限情報DTO
     */
    private UserPermissionsResponseV2.PermissionInfo convertToPermissionInfo(Permission permission) {
        String permissionCode = permission.getPermissionCode();

        // 共通解析ロジックを使用
        PermissionCodeParser.ParsedPermissionCode parsed = PermissionCodeParser.parse(permissionCode);

        // 新レスポンス構造では数値形式を使用
        return UserPermissionsResponseV2.PermissionInfo.builder()
            .permissionCode(parsed.getPermissionCode())
            .fileType(parsed.getFileTypeCode())           // 数値形式（1-4）
            .operationDivision(parsed.getOperationDivision()) // 数値形式（0=UL, 1=DL）
            .areaPattern(parsed.getAreaPattern())     // 数値形式（1=H, 2=A）
            .build();
    }

}
