package com.ms.bp.domain.permission;

import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.domain.permission.PermissionService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponse;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.security.ApiAuthMiddleware;
import com.ms.bp.shared.security.JwtTokenValidator;
import com.ms.bp.util.TestDataManager;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 権限システム集成テスト
 * 権限システムの全体的な業務フローを検証する統合テストクラス
 *
 * テスト対象：
 * - PermissionService: 権限ドメインサービス
 * - PermissionApplicationService: 権限アプリケーションサービス
 * - JwtTokenValidator: JWT認証トークン検証
 * - ApiAuthMiddleware: API認証ミドルウェア
 *
 * テストシナリオ：
 * - 正常な権限取得フロー（共通権限と個人権限の統合）
 * - 権限重複排除処理（本社権限優先ルール）
 * - 操作区分別権限フィルタリング（アップロード/ダウンロード）
 * - 権限なしユーザーの処理
 * - 無効なトークンによる認証失敗
 * - システム管理者権限の判定
 * - 兼務情報を含むロールベース権限取得
 *
 * テストデータ管理：
 * - TestDataManagerを使用してExcelファイルからテストデータを自動挿入・削除
 * - 各テストケース実行前後でデータベース状態を適切に管理
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("権限システム集成テスト")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PermissionSystemIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(PermissionSystemIntegrationTest.class);

    // テスト対象サービス（実際のインスタンス）
    private PermissionApplicationService permissionApplicationService;
    private PermissionService permissionService;

    // テストデータ管理
    private TestDataManager testDataManager;
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    @BeforeEach
    void setUp() {
        logger.info("=== 権限システム集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してリソース初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // テスト対象サービスの初期化
            permissionApplicationService = new PermissionApplicationService();

            // 権限ドメインサービスの初期化（Repository層は実際のインスタンスを使用）
            permissionService = new PermissionService(null); // Repository は LambdaResourceManager 経由で取得

            // 権限システム用テストデータ管理器の初期化
            testDataManager = new TestDataManager("permission_system_test_data.xlsx");

            // Excelからテストデータを読み込んでデータベースに挿入
            insertedDataTracker = testDataManager.insertAllTestData();

            logger.info("=== 権限システム集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("権限システムテストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("権限システムテストセットアップに失敗しました", e);
        }
    }

    @AfterEach
    void tearDown() {
        logger.info("=== 権限システム集成テストクリーンアップ開始 ===");

        if (insertedDataTracker != null) {
            logger.info("権限システムテストデータクリーンアップを実行します: {} テーブル", insertedDataTracker.size());
            testDataManager.deleteAllTestData(insertedDataTracker);
            logger.info("権限システムテストデータクリーンアップが完了しました");
        }

        logger.info("=== 権限システム集成テストクリーンアップ完了 ===");
    }

    // ==================== 権限取得フロー統合テスト ====================

    /**
     * 権限システム正常フロー統合テスト
     * 共通権限と個人権限の統合、権限重複排除処理を含む完全なフローを検証
     */
    @Test
    @Order(1)
    @DisplayName("権限システム正常フロー_共通権限と個人権限統合テスト")
    void testPermissionSystem_正常フロー統合() {
        logger.info("=== 権限システム正常フロー統合テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ユーザー権限を取得（操作区分指定なし）
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, null);

            // Then: 結果を検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getPermissions(), "権限リストが設定されていること");

            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();
            logger.info("取得された権限数: {}", permissions.size());

            // 期待される権限の検証
            verifyExpectedPermissions(permissions);

            // 権限重複排除処理の検証（本社権限優先ルール）
            verifyPermissionDeduplication(permissions);

            logger.info("✅ 権限システム正常フロー統合テスト完了");

        } catch (Exception e) {
            logger.error("権限システム正常フローテストエラー: {}", e.getMessage(), e);
            fail("権限システム正常フローテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * ダウンロード権限フィルタリングテスト
     * 操作区分でダウンロード権限のみを取得する処理を検証
     */
    @Test
    @Order(2)
    @DisplayName("権限システム_ダウンロード権限フィルタリングテスト")
    void testPermissionSystem_ダウンロード権限フィルタリング() {
        logger.info("=== ダウンロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ダウンロード権限のみを取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: ダウンロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();

            // 全ての権限がダウンロード権限であることを確認
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo(BusinessConstants.OPERATION_DOWNLOAD_CODE);
                logger.debug("ダウンロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ ダウンロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("ダウンロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("ダウンロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * アップロード権限フィルタリングテスト
     * 操作区分でアップロード権限のみを取得する処理を検証
     */
    @Test
    @Order(3)
    @DisplayName("権限システム_アップロード権限フィルタリングテスト")
    void testPermissionSystem_アップロード権限フィルタリング() {
        logger.info("=== アップロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: アップロード権限のみを取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, BusinessConstants.OPERATION_UPLOAD_CODE);

            // Then: アップロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();

            // 全ての権限がアップロード権限であることを確認
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo(BusinessConstants.OPERATION_UPLOAD_CODE);
                logger.debug("アップロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ アップロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("アップロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("アップロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 権限なしユーザーテスト
     * 権限が設定されていないユーザーの処理を検証
     */
    @Test
    @Order(4)
    @DisplayName("権限システム_権限なしユーザーテスト")
    void testPermissionSystem_権限なしユーザー() {
        logger.info("=== 権限なしユーザーテスト開始 ===");

        try {
            // Given: 権限が設定されていないユーザー情報を準備
            UserInfo noPermissionUser = createTestUserInfo("999999", "100001", "99999", "9999");

            // When: ユーザー権限を取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    noPermissionUser, null);

            // Then: 空の権限リストが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();
            assertThat(permissions).isEmpty();

            logger.info("✅ 権限なしユーザーテスト完了: 権限数=0");

        } catch (Exception e) {
            logger.error("権限なしユーザーテストエラー: {}", e.getMessage(), e);
            fail("権限なしユーザーテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 新業務要件対応版権限取得テスト（V2レスポンス）
     * システム管理者権限とロールベース権限取得を検証
     */
    @Test
    @Order(5)
    @DisplayName("権限システム_新業務要件対応版権限取得テスト")
    void testPermissionSystem_新業務要件対応版権限取得() {
        logger.info("=== 新業務要件対応版権限取得テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: 新業務要件対応版でユーザー権限を取得（一般ユーザー）
            UserPermissionsResponseV2 response = permissionApplicationService.getUserPermissions(
                    testUserInfo, "12345", "01", "0202", "G001", "0", BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: 結果を検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertThat(response.getSystemAdminFlag()).isEqualTo("0"); // 一般ユーザー
            assertNotNull(response.getRoleList(), "ロールリストが設定されていること");
            assertThat(response.getRoleList()).isNotEmpty();

            // ロール情報の検証
            UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
            assertNotNull(roleInfo.getPermissions(), "権限リストが設定されていること");

            logger.info("✅ 新業務要件対応版権限取得テスト完了: ロール数={}", response.getRoleList().size());

        } catch (Exception e) {
            logger.error("新業務要件対応版権限取得テストエラー: {}", e.getMessage(), e);
            fail("新業務要件対応版権限取得テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== JWT認証・セキュリティテスト ====================

    /**
     * JWT認証トークン検証テスト
     * 無効なトークンによる認証失敗を検証
     */
    @Test
    @Order(6)
    @DisplayName("JWT認証_無効トークン検証テスト")
    void testJwtAuthentication_無効トークン検証() {
        logger.info("=== JWT認証無効トークン検証テスト開始 ===");

        try {
            // Given: 無効なJWTトークンを準備
            String invalidToken = "invalid.jwt.token";

            // When & Then: 無効なトークンで認証を試行し、ServiceExceptionが発生することを検証
            ServiceException exception = assertThrows(ServiceException.class, () -> {
                JwtTokenValidator.validateToken(invalidToken);
            });

            // エラーコードとメッセージの検証
            assertNotNull(exception.getErrorCode(), "エラーコードが設定されていること");
            assertNotNull(exception.getMessage(), "エラーメッセージが設定されていること");
            logger.info("期待通りの認証エラーが発生: {}", exception.getMessage());

            logger.info("✅ JWT認証無効トークン検証テスト完了");

        } catch (Exception e) {
            logger.error("JWT認証無効トークン検証テストエラー: {}", e.getMessage(), e);
            fail("JWT認証無効トークン検証テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * JWT認証トークン形式検証テスト
     * トークン形式の妥当性検証を確認
     */
    @Test
    @Order(7)
    @DisplayName("JWT認証_トークン形式検証テスト")
    void testJwtAuthentication_トークン形式検証() {
        logger.info("=== JWTトークン形式検証テスト開始 ===");

        try {
            // Given: 不正な形式のトークンを準備
            String malformedToken = "Bearer invalid-format-token";

            // When: トークン形式検証を実行
            JwtTokenValidator.TokenValidationResult result = JwtTokenValidator.validateTokenFormat(malformedToken);

            // Then: 検証結果を確認
            assertNotNull(result, "検証結果が取得できること");
            // 形式が正しくない場合の処理を検証（実装に応じて調整）

            logger.info("✅ JWTトークン形式検証テスト完了");

        } catch (Exception e) {
            logger.error("JWTトークン形式検証テストエラー: {}", e.getMessage(), e);
            fail("JWTトークン形式検証テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== 権限コード解析・重複排除テスト ====================

    /**
     * 権限コード解析テスト
     * PermissionCodeParserの解析ロジックを検証
     */
    @Test
    @Order(8)
    @DisplayName("権限システム_権限コード解析テスト")
    void testPermissionSystem_権限コード解析() {
        logger.info("=== 権限コード解析テスト開始 ===");

        try {
            // Given: 各種権限コードを準備
            String downloadHeadOfficeCode = "DLH001"; // ダウンロード・本社・計画マスタ
            String uploadAreaCode = "ULA002";         // アップロード・エリア・見通し計画本社
            String invalidCode = "INVALID";           // 無効なコード

            // When & Then: 正常な権限コードの解析を検証
            UserPermissionInfo downloadPermission = PermissionService.parsePermissionCode(downloadHeadOfficeCode);
            assertThat(downloadPermission.getPermissionCode()).isEqualTo(downloadHeadOfficeCode);
            assertThat(downloadPermission.getOperationDivision()).isEqualTo(BusinessConstants.OPERATION_DOWNLOAD_CODE);
            assertThat(downloadPermission.getAreaPattern()).isEqualTo(BusinessConstants.AFFILIATION_HEAD_OFFICE);
            assertThat(downloadPermission.getFileType()).isEqualTo(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);

            UserPermissionInfo uploadPermission = PermissionService.parsePermissionCode(uploadAreaCode);
            assertThat(uploadPermission.getPermissionCode()).isEqualTo(uploadAreaCode);
            assertThat(uploadPermission.getOperationDivision()).isEqualTo(BusinessConstants.OPERATION_UPLOAD_CODE);
            assertThat(uploadPermission.getAreaPattern()).isEqualTo(BusinessConstants.AFFILIATION_AREA);
            assertThat(uploadPermission.getFileType()).isEqualTo(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE);

            // 無効なコードの場合の例外処理を検証
            assertThrows(IllegalArgumentException.class, () -> {
                PermissionService.parsePermissionCode(invalidCode);
            });

            logger.info("✅ 権限コード解析テスト完了");

        } catch (Exception e) {
            logger.error("権限コード解析テストエラー: {}", e.getMessage(), e);
            fail("権限コード解析テストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 権限重複排除テスト
     * 本社権限優先ルールによる重複排除処理を検証
     */
    @Test
    @Order(9)
    @DisplayName("権限システム_権限重複排除テスト")
    void testPermissionSystem_権限重複排除() {
        logger.info("=== 権限重複排除テスト開始 ===");

        try {
            // Given: 重複する権限リストを準備（本社権限とエリア権限が混在）
            List<UserPermissionInfo> duplicatePermissions = List.of(
                createPermissionInfo("DLH001", BusinessConstants.OPERATION_DOWNLOAD_CODE,
                                   BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE),
                createPermissionInfo("DLA001", BusinessConstants.OPERATION_DOWNLOAD_CODE,
                                   BusinessConstants.AFFILIATION_AREA, BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE),
                createPermissionInfo("ULH002", BusinessConstants.OPERATION_UPLOAD_CODE,
                                   BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE),
                createPermissionInfo("ULA003", BusinessConstants.OPERATION_UPLOAD_CODE,
                                   BusinessConstants.AFFILIATION_AREA, BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE)
            );

            // When: 重複排除処理を実行
            List<UserPermissionInfo> deduplicatedPermissions = PermissionService.deduplicatePermissions(duplicatePermissions);

            // Then: 本社権限が優先され、対応するエリア権限が除外されることを検証
            assertThat(deduplicatedPermissions).hasSize(3); // DLA001が除外される

            // 本社権限が残っていることを確認
            assertThat(deduplicatedPermissions.stream()
                .anyMatch(p -> "DLH001".equals(p.getPermissionCode()))).isTrue();

            // 対応するエリア権限が除外されていることを確認
            assertThat(deduplicatedPermissions.stream()
                .anyMatch(p -> "DLA001".equals(p.getPermissionCode()))).isFalse();

            logger.info("✅ 権限重複排除テスト完了: 重複排除後権限数={}", deduplicatedPermissions.size());

        } catch (Exception e) {
            logger.error("権限重複排除テストエラー: {}", e.getMessage(), e);
            fail("権限重複排除テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== プライベートヘルパーメソッド ====================

    /**
     * テスト用ユーザー情報を作成
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @param unitCode ユニットコード
     * @param areaCode エリアコード
     * @return テスト用ユーザー情報
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode,
                                       String unitCode, String areaCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        userInfo.setUnitCode(unitCode);
        userInfo.setAreaCode(areaCode);
        return userInfo;
    }

    /**
     * テスト用権限情報を作成
     *
     * @param permissionCode 権限コード
     * @param operationDivision 操作区分
     * @param areaPattern エリアパターン
     * @param fileType ファイル種別
     * @return テスト用権限情報
     */
    private UserPermissionInfo createPermissionInfo(String permissionCode, String operationDivision,
                                                   String areaPattern, String fileType) {
        UserPermissionInfo info = new UserPermissionInfo();
        info.setPermissionCode(permissionCode);
        info.setOperationDivision(operationDivision);
        info.setAreaPattern(areaPattern);
        info.setFileType(fileType);
        return info;
    }

    /**
     * 期待される権限の検証
     * 取得された権限リストが期待される内容を含んでいることを検証
     *
     * @param permissions 検証対象の権限リスト
     */
    private void verifyExpectedPermissions(List<UserPermissionsResponse.PermissionInfo> permissions) {
        assertThat(permissions).isNotEmpty();

        // 各権限の基本項目が設定されていることを確認
        for (UserPermissionsResponse.PermissionInfo permission : permissions) {
            // 権限コードが設定されていることを確認
            assertThat(permission.getPermissionCode()).isNotNull().isNotEmpty();

            // 操作区分が正しく設定されていることを確認
            assertThat(permission.getOperationDivision())
                    .isIn(BusinessConstants.OPERATION_DOWNLOAD_CODE, BusinessConstants.OPERATION_UPLOAD_CODE);

            // エリアパターンが正しく設定されていることを確認
            assertThat(permission.getAreaPattern())
                    .isIn(BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.AFFILIATION_AREA);

            // ファイル種別コードが設定されていることを確認
            assertThat(permission.getFileTypeCode()).isNotNull();

            logger.debug("権限検証完了: コード={}, 操作={}, エリア={}, ファイル種別={}",
                    permission.getPermissionCode(),
                    permission.getOperationDivision(),
                    permission.getAreaPattern(),
                    permission.getFileTypeCode());
        }
    }

    /**
     * 権限重複排除処理の検証
     * 本社権限優先ルールが正しく適用されていることを検証
     *
     * @param permissions 検証対象の権限リスト
     */
    private void verifyPermissionDeduplication(List<UserPermissionsResponse.PermissionInfo> permissions) {
        // 権限コードのペアを確認（本社権限とエリア権限の重複チェック）
        Map<String, List<UserPermissionsResponse.PermissionInfo>> groupedByBaseCode = permissions.stream()
                .collect(Collectors.groupingBy(p -> {
                    String code = p.getPermissionCode();
                    // 3桁目を除いた基本コードでグループ化
                    return code.substring(0, 2) + code.substring(3);
                }));

        // 同じ基本コードで本社権限とエリア権限が共存していないことを確認
        groupedByBaseCode.forEach((baseCode, permissionGroup) -> {
            boolean hasHeadOffice = permissionGroup.stream()
                    .anyMatch(p -> p.getPermissionCode().charAt(2) == 'H');
            boolean hasArea = permissionGroup.stream()
                    .anyMatch(p -> p.getPermissionCode().charAt(2) == 'A');

            if (hasHeadOffice && hasArea) {
                fail(String.format("権限重複排除が正しく動作していません。基本コード %s で本社権限とエリア権限が共存しています", baseCode));
            }

            logger.debug("権限重複排除検証完了: 基本コード={}, 権限数={}", baseCode, permissionGroup.size());
        });

        logger.info("権限重複排除処理が正しく動作していることを確認しました");
    }

    /**
     * システム管理者権限の検証
     * システム管理者フラグと権限内容が正しく設定されていることを検証
     *
     * @param response 検証対象のレスポンス
     */
    private void verifySystemAdminPermissions(UserPermissionsResponseV2 response) {
        assertThat(response.getSystemAdminFlag()).isEqualTo("1");
        assertThat(response.getScreenDisplayFlag()).isEqualTo("0"); // 表示可
        assertNotNull(response.getRoleList());
        assertThat(response.getRoleList()).isNotEmpty();

        // システム管理者の場合、全ての権限が設定されていることを確認
        UserPermissionsResponseV2.RoleInfo roleInfo = response.getRoleList().get(0);
        assertNotNull(roleInfo.getPermissions());
        assertThat(roleInfo.getPermissions()).isNotEmpty();

        logger.info("システム管理者権限検証完了: ロール数={}, 権限数={}",
                   response.getRoleList().size(),
                   roleInfo.getPermissions().size());
    }

    /**
     * 一般ユーザー権限の検証
     * 一般ユーザーフラグと権限内容が正しく設定されていることを検証
     *
     * @param response 検証対象のレスポンス
     */
    private void verifyGeneralUserPermissions(UserPermissionsResponseV2 response) {
        assertThat(response.getSystemAdminFlag()).isEqualTo("0");
        assertNotNull(response.getRoleList());

        // 権限がある場合は表示可、ない場合は表示不可
        if (response.getRoleList().isEmpty() ||
            response.getRoleList().stream().allMatch(role -> role.getPermissions().isEmpty())) {
            assertThat(response.getScreenDisplayFlag()).isEqualTo("0"); // 権限なしでも表示可（業務要件による）
        } else {
            assertThat(response.getScreenDisplayFlag()).isEqualTo("1"); // 表示可
        }

        logger.info("一般ユーザー権限検証完了: ロール数={}", response.getRoleList().size());
    }
}
