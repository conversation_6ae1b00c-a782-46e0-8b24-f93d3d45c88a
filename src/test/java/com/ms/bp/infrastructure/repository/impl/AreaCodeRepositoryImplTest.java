package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AreaCodeRepositoryImpl単体テスト
 * findAreaNameByAreaCodeメソッドの動作を検証
 */
class AreaCodeRepositoryImplTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    private AreaCodeRepository areaCodeRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        areaCodeRepository = new AreaCodeRepositoryImpl(jdbcTemplate);
    }

    @Test
    @DisplayName("エリアコードでエリア名称を正常に取得できること")
    void testFindAreaNameByAreaCode_Success() {
        // テスト用データを準備
        String testAreaCode = "1000";
        String expectedAreaName = "東京エリア";

        // JdbcTemplateのqueryメソッドをモック
        when(jdbcTemplate.query(anyString(), any(Object[].class), any()))
            .thenReturn(java.util.List.of(expectedAreaName));

        // メソッド実行
        Optional<String> result = areaCodeRepository.findAreaNameByAreaCode(testAreaCode);

        // 結果検証
        assertTrue(result.isPresent(), "エリア名称が取得できること");
        assertEquals(expectedAreaName, result.get(), "正しいエリア名称が取得できること");

        // JdbcTemplateが正しく呼び出されたことを検証
        verify(jdbcTemplate, times(1)).query(anyString(), any(Object[].class), any());
    }

    @Test
    @DisplayName("存在しないエリアコードの場合は空のOptionalが返されること")
    void testFindAreaNameByAreaCode_NotFound() {
        // テスト用データを準備
        String testAreaCode = "9999";

        // JdbcTemplateのqueryメソッドをモック（空のリストを返す）
        when(jdbcTemplate.query(anyString(), any(Object[].class), any()))
            .thenReturn(java.util.List.of());

        // メソッド実行
        Optional<String> result = areaCodeRepository.findAreaNameByAreaCode(testAreaCode);

        // 結果検証
        assertFalse(result.isPresent(), "存在しないエリアコードの場合は空のOptionalが返されること");

        // JdbcTemplateが正しく呼び出されたことを検証
        verify(jdbcTemplate, times(1)).query(anyString(), any(Object[].class), any());
    }

    @Test
    @DisplayName("nullまたは空のエリアコードでも正常に処理されること")
    void testFindAreaNameByAreaCode_NullOrEmpty() {
        // nullの場合
        when(jdbcTemplate.query(anyString(), any(Object[].class), any()))
            .thenReturn(java.util.List.of());

        Optional<String> result1 = areaCodeRepository.findAreaNameByAreaCode(null);
        assertFalse(result1.isPresent(), "nullエリアコードの場合は空のOptionalが返されること");

        // 空文字の場合
        Optional<String> result2 = areaCodeRepository.findAreaNameByAreaCode("");
        assertFalse(result2.isPresent(), "空エリアコードの場合は空のOptionalが返されること");
    }
}
